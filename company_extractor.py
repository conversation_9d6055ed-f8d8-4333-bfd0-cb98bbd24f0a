#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公司实体抽取工具
支持多种方法：NER模型、规则匹配、关键词匹配
"""

import re
import jieba
import pandas as pd
from typing import List, Dict, Set
from collections import Counter

class CompanyExtractor:
    def __init__(self):
        """初始化公司实体抽取器"""
        self.company_keywords = {
            '公司', '集团', '企业', '有限公司', '股份有限公司', 
            '科技', '银行', '保险', '证券', '基金', '投资',
            'Co.', 'Corp.', 'Inc.', 'Ltd.', 'Group'
        }
        
        # 公司名称正则模式
        self.company_patterns = [
            # 中文公司名
            r'[\u4e00-\u9fff]{2,10}(?:公司|集团|企业|有限公司|股份有限公司)',
            r'[\u4e00-\u9fff]{2,8}(?:银行|保险|证券|基金|投资|科技|电子|通信|网络)',
            # 英文公司名
            r'[A-Z][a-zA-Z\s]{2,20}(?:Inc\.|Corp\.|Ltd\.|Co\.|Group)',
            # 混合模式
            r'[\u4e00-\u9fff]{1,5}[A-Z][a-zA-Z]+[\u4e00-\u9fff]*(?:公司|集团)?'
        ]
        
        # 编译正则表达式
        self.compiled_patterns = [re.compile(pattern) for pattern in self.company_patterns]
        
        # 常见公司后缀
        self.company_suffixes = {
            '公司', '集团', '企业', '有限公司', '股份有限公司',
            '银行', '保险', '证券', '基金', '投资', '科技',
            'Inc.', 'Corp.', 'Ltd.', 'Co.', 'Group'
        }

    def extract_by_rules(self, text: str) -> List[str]:
        """基于规则的公司名抽取"""
        companies = []
        
        # 使用正则模式匹配
        for pattern in self.compiled_patterns:
            matches = pattern.findall(text)
            companies.extend(matches)
        
        # 去重并过滤
        companies = list(set(companies))
        companies = [comp for comp in companies if len(comp) >= 2]
        
        return companies

    def extract_by_keywords(self, text: str) -> List[str]:
        """基于关键词的公司名抽取"""
        companies = []
        
        # 分词
        words = jieba.lcut(text)
        
        # 查找包含公司关键词的词组
        for i, word in enumerate(words):
            if any(keyword in word for keyword in self.company_keywords):
                # 向前查找可能的公司名前缀
                start_idx = max(0, i-3)
                end_idx = min(len(words), i+2)
                
                potential_company = ''.join(words[start_idx:end_idx])
                
                # 简单验证
                if self._is_valid_company_name(potential_company):
                    companies.append(potential_company)
        
        return list(set(companies))

    def _is_valid_company_name(self, name: str) -> bool:
        """验证是否为有效的公司名"""
        # 长度检查
        if len(name) < 2 or len(name) > 50:
            return False
        
        # 包含公司关键词
        if not any(keyword in name for keyword in self.company_keywords):
            return False
        
        # 不能全是数字或特殊字符
        if re.match(r'^[\d\W]+$', name):
            return False
        
        return True

    def extract_companies(self, text: str, method: str = 'hybrid') -> Dict:
        """
        抽取公司实体
        
        Args:
            text: 输入文本
            method: 抽取方法 ('rules', 'keywords', 'hybrid')
        
        Returns:
            包含抽取结果的字典
        """
        result = {
            'text': text,
            'companies': [],
            'method': method,
            'confidence_scores': {}
        }
        
        if method == 'rules':
            companies = self.extract_by_rules(text)
        elif method == 'keywords':
            companies = self.extract_by_keywords(text)
        elif method == 'hybrid':
            # 混合方法：结合规则和关键词
            rules_companies = self.extract_by_rules(text)
            keywords_companies = self.extract_by_keywords(text)
            
            # 合并结果并计算置信度
            all_companies = rules_companies + keywords_companies
            company_counts = Counter(all_companies)
            
            companies = []
            confidence_scores = {}
            
            for company, count in company_counts.items():
                companies.append(company)
                # 出现在多种方法中的公司置信度更高
                confidence_scores[company] = min(count / 2.0, 1.0)
            
            result['confidence_scores'] = confidence_scores
        else:
            raise ValueError("方法必须是 'rules', 'keywords', 或 'hybrid'")
        
        result['companies'] = list(set(companies))
        return result

    def batch_extract(self, texts: List[str], method: str = 'hybrid') -> List[Dict]:
        """批量抽取公司实体"""
        results = []
        for text in texts:
            result = self.extract_companies(text, method)
            results.append(result)
        return results

    def extract_from_dataframe(self, df: pd.DataFrame, text_column: str, 
                             method: str = 'hybrid') -> pd.DataFrame:
        """从DataFrame中抽取公司实体"""
        results = []
        
        for idx, row in df.iterrows():
            text = str(row[text_column])
            result = self.extract_companies(text, method)
            
            # 添加原始数据
            result.update({
                'index': idx,
                'original_data': row.to_dict()
            })
            results.append(result)
        
        return pd.DataFrame(results)


def demo_usage():
    """演示用法"""
    extractor = CompanyExtractor()
    
    # 测试文本
    test_texts = [
        "腾讯公司今日发布财报，显示营收同比增长15%。阿里巴巴集团股价上涨3%。",
        "中国平安保险公司与华为科技达成战略合作协议。",
        "Apple Inc. 和 Microsoft Corp. 在人工智能领域展开竞争。",
        "招商银行、工商银行等金融机构纷纷布局数字化转型。"
    ]
    
    print("=== 公司实体抽取演示 ===\n")
    
    for i, text in enumerate(test_texts, 1):
        print(f"文本 {i}: {text}")
        result = extractor.extract_companies(text, method='hybrid')
        
        print(f"抽取的公司: {result['companies']}")
        if result['confidence_scores']:
            print("置信度分数:")
            for company, score in result['confidence_scores'].items():
                print(f"  {company}: {score:.2f}")
        print("-" * 50)


if __name__ == "__main__":
    demo_usage()
