import pymysql as sql
import pandas as pd
import sqlalchemy

# 2023-12-06之前的数据保存在media_database里，之后的数据保存在E_newspapers里
dbname = "E_newspapers" #"media_database"
username = "root"
password = "0e84a030e103503ef2da6ff7422700be66adf8dcaa5298889bcbea98b4199b20"
host = "************"
port = 9201

conn_str = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(username, password, host, port, dbname)
myengine = sqlalchemy.create_engine(conn_str)

# 从db获取数据表名
def get_table_from_db(myengine):
    insp  = sqlalchemy.inspect(myengine)
    tables = insp.get_table_names(dbname)
    return tables

# 从table获取数据
def get_data_from_table(myengine, table_name=""):
    if table_name=="":
        print("Table name is NULL")
        return
    sql_cont = pd.read_sql_table(table_name=table_name, con=myengine.connect())
    if sql_cont.size==0:
            print("The table({}) is NULL!".format(table_name))
            return
    else:
        data = []
        sql_dict = sql_cont.to_dict("split")
        for dd in sql_dict["data"]:
            dd_dict = dict(zip(sql_dict["columns"], dd))
            if dd_dict not in data:
                data.append(dd_dict)
        return data


if __name__=="__main__":
    tables = get_table_from_db(myengine=myengine)
    print(tables)
    # data： list
    # data = get_data_from_table(myengine=myengine, table_name="ChinaBookNews")