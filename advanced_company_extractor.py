#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级公司实体抽取工具
支持多种先进的NLP方法
"""

import re
import jieba
import pandas as pd
from typing import List, Dict, Set, Optional, Tuple
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

class AdvancedCompanyExtractor:
    def __init__(self, use_spacy: bool = False, use_hanlp: bool = False):
        """
        初始化高级公司实体抽取器
        
        Args:
            use_spacy: 是否使用spaCy模型
            use_hanlp: 是否使用HanLP模型
        """
        self.use_spacy = use_spacy
        self.use_hanlp = use_hanlp
        
        # 初始化基础组件
        self._init_basic_components()
        
        # 初始化高级模型
        if use_spacy:
            self._init_spacy()
        if use_hanlp:
            self._init_hanlp()
    
    def _init_basic_components(self):
        """初始化基础组件"""
        # 公司关键词
        self.company_keywords = {
            '公司', '集团', '企业', '有限公司', '股份有限公司', 
            '科技', '银行', '保险', '证券', '基金', '投资',
            '电子', '通信', '网络', '软件', '互联网',
            'Co.', 'Corp.', 'Inc.', 'Ltd.', 'Group', 'Holdings'
        }
        
        # 行业关键词
        self.industry_keywords = {
            '科技': ['科技', '技术', '软件', '互联网', '电子', '通信', '网络'],
            '金融': ['银行', '保险', '证券', '基金', '投资', '金融'],
            '制造': ['制造', '生产', '工业', '机械', '汽车'],
            '房地产': ['房地产', '地产', '置业', '建设'],
            '医药': ['医药', '制药', '生物', '医疗'],
            '能源': ['能源', '石油', '电力', '煤炭', '新能源']
        }
        
        # 公司名称正则模式
        self.company_patterns = [
            # 标准中文公司名
            r'[\u4e00-\u9fff]{2,15}(?:有限公司|股份有限公司|集团有限公司)',
            r'[\u4e00-\u9fff]{2,10}(?:公司|集团|企业)',
            # 行业特定模式
            r'[\u4e00-\u9fff]{2,8}(?:银行|保险|证券|基金|投资)(?:有限公司|股份有限公司)?',
            r'[\u4e00-\u9fff]{2,8}(?:科技|电子|通信|网络|软件)(?:有限公司|股份有限公司)?',
            # 英文公司名
            r'[A-Z][a-zA-Z\s&]{2,25}(?:Inc\.|Corp\.|Ltd\.|Co\.|Group|Holdings)',
            # 混合模式
            r'[\u4e00-\u9fff]{1,8}[A-Z][a-zA-Z]+(?:[\u4e00-\u9fff]*)?(?:公司|集团)?'
        ]
        
        self.compiled_patterns = [re.compile(pattern) for pattern in self.company_patterns]
    
    def _init_spacy(self):
        """初始化spaCy模型"""
        try:
            import spacy
            self.nlp_spacy = spacy.load("zh_core_web_sm")
            print("spaCy中文模型加载成功")
        except ImportError:
            print("spaCy未安装，请运行: pip install spacy")
            print("然后下载中文模型: python -m spacy download zh_core_web_sm")
            self.use_spacy = False
        except OSError:
            print("spaCy中文模型未找到，请运行: python -m spacy download zh_core_web_sm")
            self.use_spacy = False
    
    def _init_hanlp(self):
        """初始化HanLP模型"""
        try:
            import hanlp
            self.nlp_hanlp = hanlp.load(hanlp.pretrained.ner.MSRA_NER_BERT_BASE_ZH)
            print("HanLP模型加载成功")
        except ImportError:
            print("HanLP未安装，请运行: pip install hanlp")
            self.use_hanlp = False
        except Exception as e:
            print(f"HanLP模型加载失败: {e}")
            self.use_hanlp = False
    
    def extract_by_spacy(self, text: str) -> List[Dict]:
        """使用spaCy进行实体抽取"""
        if not self.use_spacy:
            return []
        
        companies = []
        doc = self.nlp_spacy(text)
        
        for ent in doc.ents:
            if ent.label_ in ["ORG", "PERSON"]:  # 组织或可能的公司名
                company_info = {
                    'text': ent.text,
                    'label': ent.label_,
                    'start': ent.start_char,
                    'end': ent.end_char,
                    'confidence': 0.8,  # spaCy默认置信度
                    'method': 'spacy'
                }
                
                # 验证是否为公司
                if self._is_likely_company(ent.text):
                    company_info['confidence'] = 0.9
                    companies.append(company_info)
        
        return companies
    
    def extract_by_hanlp(self, text: str) -> List[Dict]:
        """使用HanLP进行实体抽取"""
        if not self.use_hanlp:
            return []
        
        companies = []
        try:
            result = self.nlp_hanlp([text])
            
            if result and len(result) > 0:
                entities = result[0]
                for entity in entities:
                    if len(entity) >= 3:  # [start, end, label]
                        start, end, label = entity[0], entity[1], entity[2]
                        entity_text = text[start:end]
                        
                        if label in ["ORG", "ORGANIZATION"] or self._is_likely_company(entity_text):
                            company_info = {
                                'text': entity_text,
                                'label': label,
                                'start': start,
                                'end': end,
                                'confidence': 0.85,
                                'method': 'hanlp'
                            }
                            companies.append(company_info)
        
        except Exception as e:
            print(f"HanLP处理出错: {e}")
        
        return companies
    
    def extract_by_rules(self, text: str) -> List[Dict]:
        """基于规则的公司名抽取（增强版）"""
        companies = []
        
        # 使用正则模式匹配
        for i, pattern in enumerate(self.compiled_patterns):
            for match in pattern.finditer(text):
                company_info = {
                    'text': match.group(),
                    'label': 'ORG',
                    'start': match.start(),
                    'end': match.end(),
                    'confidence': 0.7 + i * 0.05,  # 不同模式不同置信度
                    'method': 'rules'
                }
                companies.append(company_info)
        
        return companies
    
    def _is_likely_company(self, text: str) -> bool:
        """判断文本是否可能是公司名"""
        # 长度检查
        if len(text) < 2 or len(text) > 50:
            return False
        
        # 包含公司关键词
        if any(keyword in text for keyword in self.company_keywords):
            return True
        
        # 包含行业关键词
        for industry, keywords in self.industry_keywords.items():
            if any(keyword in text for keyword in keywords):
                return True
        
        # 中文字符比例检查（对于中文公司名）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        if chinese_chars / len(text) > 0.5 and chinese_chars >= 2:
            return True
        
        # 英文公司名模式
        if re.match(r'^[A-Z][a-zA-Z\s&]+(?:Inc|Corp|Ltd|Co|Group)\.?$', text):
            return True
        
        return False
    
    def _merge_overlapping_entities(self, entities: List[Dict]) -> List[Dict]:
        """合并重叠的实体"""
        if not entities:
            return []
        
        # 按起始位置排序
        entities.sort(key=lambda x: x['start'])
        
        merged = []
        current = entities[0]
        
        for next_entity in entities[1:]:
            # 检查是否重叠
            if next_entity['start'] <= current['end']:
                # 选择置信度更高或更长的实体
                if (next_entity['confidence'] > current['confidence'] or 
                    (next_entity['end'] - next_entity['start']) > (current['end'] - current['start'])):
                    current = next_entity
            else:
                merged.append(current)
                current = next_entity
        
        merged.append(current)
        return merged
    
    def extract_companies_advanced(self, text: str, 
                                 methods: List[str] = None) -> Dict:
        """
        高级公司实体抽取
        
        Args:
            text: 输入文本
            methods: 使用的方法列表，可选: ['spacy', 'hanlp', 'rules']
        
        Returns:
            包含详细抽取结果的字典
        """
        if methods is None:
            methods = []
            if self.use_spacy:
                methods.append('spacy')
            if self.use_hanlp:
                methods.append('hanlp')
            methods.append('rules')  # 总是包含规则方法
        
        all_entities = []
        method_results = {}
        
        # 使用不同方法抽取
        for method in methods:
            if method == 'spacy' and self.use_spacy:
                entities = self.extract_by_spacy(text)
                method_results['spacy'] = entities
                all_entities.extend(entities)
            elif method == 'hanlp' and self.use_hanlp:
                entities = self.extract_by_hanlp(text)
                method_results['hanlp'] = entities
                all_entities.extend(entities)
            elif method == 'rules':
                entities = self.extract_by_rules(text)
                method_results['rules'] = entities
                all_entities.extend(entities)
        
        # 合并重叠实体
        merged_entities = self._merge_overlapping_entities(all_entities)
        
        # 计算最终置信度（如果多个方法都识别出同一实体，提高置信度）
        entity_counts = Counter([entity['text'] for entity in all_entities])
        for entity in merged_entities:
            count = entity_counts[entity['text']]
            if count > 1:
                entity['confidence'] = min(entity['confidence'] + (count - 1) * 0.1, 1.0)
                entity['method'] = f"{entity['method']}+{count-1}others"
        
        # 按置信度排序
        merged_entities.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 提取公司名列表
        companies = [entity['text'] for entity in merged_entities]
        
        result = {
            'text': text,
            'companies': companies,
            'detailed_entities': merged_entities,
            'method_results': method_results,
            'methods_used': methods,
            'total_entities': len(merged_entities)
        }
        
        return result
    
    def classify_companies_by_industry(self, companies: List[str]) -> Dict[str, List[str]]:
        """按行业分类公司"""
        industry_classification = {industry: [] for industry in self.industry_keywords.keys()}
        industry_classification['其他'] = []
        
        for company in companies:
            classified = False
            for industry, keywords in self.industry_keywords.items():
                if any(keyword in company for keyword in keywords):
                    industry_classification[industry].append(company)
                    classified = True
                    break
            
            if not classified:
                industry_classification['其他'].append(company)
        
        # 移除空分类
        return {k: v for k, v in industry_classification.items() if v}


def demo_advanced():
    """演示高级功能"""
    # 尝试使用高级模型
    extractor = AdvancedCompanyExtractor(use_spacy=True, use_hanlp=True)
    
    test_texts = [
        "腾讯控股有限公司今日发布财报，阿里巴巴集团控股有限公司股价上涨。",
        "中国平安保险(集团)股份有限公司与华为技术有限公司达成合作。",
        "Apple Inc. 和 Microsoft Corporation 在AI领域竞争激烈。",
        "招商银行、中国工商银行等金融机构布局数字化转型。"
    ]
    
    print("=== 高级公司实体抽取演示 ===\n")
    
    for i, text in enumerate(test_texts, 1):
        print(f"文本 {i}: {text}")
        result = extractor.extract_companies_advanced(text)
        
        print(f"发现公司: {result['companies']}")
        print(f"使用方法: {result['methods_used']}")
        
        if result['detailed_entities']:
            print("详细信息:")
            for entity in result['detailed_entities']:
                print(f"  {entity['text']} (置信度: {entity['confidence']:.2f}, 方法: {entity['method']})")
        
        # 行业分类
        if result['companies']:
            industry_classification = extractor.classify_companies_by_industry(result['companies'])
            print("行业分类:")
            for industry, companies in industry_classification.items():
                print(f"  {industry}: {companies}")
        
        print("-" * 60)


if __name__ == "__main__":
    demo_advanced()
