import json


#读取park_gx.json文件
with open('park_gx.json', 'r', encoding='utf-8') as f:
    data_gx = json.load(f)
# 读取park_jk.json文件
with open('park_jk.json', 'r', encoding='utf-8') as f:
    data_jk = json.load(f)
# 根据数据中的value，去重，将data_gx和data_jk合并
data = data_gx + data_jk
data = list(set([json.dumps(i) for i in data]))
data = [json.loads(i) for i in data]
# 将合并后的数据写入park.json文件
with open('park.json', 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)