#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻公司实体分析工具
集成数据库连接和公司实体抽取功能
"""

import pandas as pd
import sqlalchemy
from typing import List, Dict, Optional
from company_extractor import CompanyExtractor
from dataset import myengine, get_data_from_table, get_table_from_db

class NewsCompanyAnalyzer:
    def __init__(self, engine=None):
        """
        初始化新闻公司分析器
        
        Args:
            engine: SQLAlchemy引擎，如果为None则使用默认引擎
        """
        self.engine = engine or myengine
        self.extractor = CompanyExtractor()
    
    def get_news_data(self, table_name: str, limit: Optional[int] = None) -> pd.DataFrame:
        """
        从数据库获取新闻数据
        
        Args:
            table_name: 表名
            limit: 限制返回的记录数
        
        Returns:
            包含新闻数据的DataFrame
        """
        try:
            if limit:
                query = f"SELECT * FROM {table_name} LIMIT {limit}"
                df = pd.read_sql(query, self.engine)
            else:
                df = pd.read_sql_table(table_name, self.engine)
            
            print(f"成功获取 {len(df)} 条记录从表 {table_name}")
            return df
        
        except Exception as e:
            print(f"获取数据时出错: {e}")
            return pd.DataFrame()
    
    def analyze_news_companies(self, table_name: str, text_columns: List[str], 
                             limit: Optional[int] = None, 
                             method: str = 'hybrid') -> Dict:
        """
        分析新闻中的公司实体
        
        Args:
            table_name: 数据表名
            text_columns: 包含文本内容的列名列表（如标题、内容等）
            limit: 限制处理的记录数
            method: 抽取方法
        
        Returns:
            分析结果字典
        """
        # 获取数据
        df = self.get_news_data(table_name, limit)
        if df.empty:
            return {"error": "无法获取数据"}
        
        print(f"数据列: {list(df.columns)}")
        
        # 检查指定的文本列是否存在
        available_columns = [col for col in text_columns if col in df.columns]
        if not available_columns:
            print(f"警告: 指定的文本列 {text_columns} 在数据中不存在")
            print(f"可用列: {list(df.columns)}")
            # 尝试自动识别可能的文本列
            text_like_columns = [col for col in df.columns 
                               if any(keyword in col.lower() 
                                     for keyword in ['title', 'content', 'text', '标题', '内容', '正文'])]
            if text_like_columns:
                available_columns = text_like_columns[:2]  # 最多取前两个
                print(f"自动选择文本列: {available_columns}")
            else:
                return {"error": "无法找到合适的文本列"}
        
        results = {
            'table_name': table_name,
            'total_records': len(df),
            'processed_records': 0,
            'companies_found': {},
            'company_frequency': {},
            'records_with_companies': [],
            'summary': {}
        }
        
        all_companies = []
        records_with_companies = []
        
        # 处理每条记录
        for idx, row in df.iterrows():
            record_companies = set()
            
            # 合并所有文本列的内容
            combined_text = ""
            for col in available_columns:
                if pd.notna(row[col]):
                    combined_text += str(row[col]) + " "
            
            if combined_text.strip():
                # 抽取公司实体
                extraction_result = self.extractor.extract_companies(
                    combined_text, method=method
                )
                
                if extraction_result['companies']:
                    record_companies.update(extraction_result['companies'])
                    all_companies.extend(extraction_result['companies'])
                    
                    # 保存包含公司的记录
                    record_info = {
                        'index': idx,
                        'companies': list(record_companies),
                        'text_preview': combined_text[:200] + "..." if len(combined_text) > 200 else combined_text,
                        'confidence_scores': extraction_result.get('confidence_scores', {})
                    }
                    
                    # 添加原始数据的关键字段
                    for col in df.columns:
                        if col in available_columns or 'id' in col.lower() or 'time' in col.lower() or 'date' in col.lower():
                            record_info[col] = row[col]
                    
                    records_with_companies.append(record_info)
        
        results['processed_records'] = len(df)
        results['records_with_companies'] = records_with_companies
        
        # 统计公司出现频率
        if all_companies:
            from collections import Counter
            company_freq = Counter(all_companies)
            results['company_frequency'] = dict(company_freq.most_common())
            results['companies_found'] = {
                'unique_companies': list(set(all_companies)),
                'total_mentions': len(all_companies),
                'unique_count': len(set(all_companies))
            }
        
        # 生成摘要
        results['summary'] = {
            'records_with_companies': len(records_with_companies),
            'coverage_rate': len(records_with_companies) / len(df) if len(df) > 0 else 0,
            'top_companies': list(results['company_frequency'].keys())[:10] if results['company_frequency'] else [],
            'avg_companies_per_record': len(all_companies) / len(records_with_companies) if records_with_companies else 0
        }
        
        return results
    
    def save_results_to_csv(self, results: Dict, output_file: str):
        """将结果保存到CSV文件"""
        if 'records_with_companies' in results and results['records_with_companies']:
            df_results = pd.DataFrame(results['records_with_companies'])
            df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"结果已保存到: {output_file}")
        else:
            print("没有找到公司实体，无法保存结果")
    
    def get_available_tables(self) -> List[str]:
        """获取可用的数据表列表"""
        return get_table_from_db(self.engine)
    
    def preview_table_structure(self, table_name: str, limit: int = 5) -> pd.DataFrame:
        """预览表结构和数据"""
        try:
            df = self.get_news_data(table_name, limit)
            if not df.empty:
                print(f"\n表 {table_name} 的结构:")
                print(f"列数: {len(df.columns)}")
                print(f"列名: {list(df.columns)}")
                print(f"\n前 {limit} 行数据:")
                return df
            return pd.DataFrame()
        except Exception as e:
            print(f"预览表时出错: {e}")
            return pd.DataFrame()


def main():
    """主函数 - 演示用法"""
    analyzer = NewsCompanyAnalyzer()
    
    # 获取可用表
    print("=== 可用数据表 ===")
    tables = analyzer.get_available_tables()
    for i, table in enumerate(tables, 1):
        print(f"{i}. {table}")
    
    if not tables:
        print("没有找到可用的数据表")
        return
    
    # 选择一个表进行分析（这里使用第一个表作为示例）
    table_name = tables[0]
    print(f"\n=== 分析表: {table_name} ===")
    
    # 预览表结构
    preview_df = analyzer.preview_table_structure(table_name, limit=3)
    if preview_df.empty:
        print("无法预览表结构")
        return
    
    print(preview_df.head())
    
    # 根据列名猜测文本列
    text_columns = []
    for col in preview_df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['title', 'content', 'text', '标题', '内容', '正文']):
            text_columns.append(col)
    
    if not text_columns:
        print("无法自动识别文本列，请手动指定")
        return
    
    print(f"识别的文本列: {text_columns}")
    
    # 进行公司实体分析
    print(f"\n=== 开始分析公司实体 ===")
    results = analyzer.analyze_news_companies(
        table_name=table_name,
        text_columns=text_columns,
        limit=100,  # 限制处理100条记录作为示例
        method='hybrid'
    )
    
    # 显示结果
    if 'error' in results:
        print(f"分析出错: {results['error']}")
        return
    
    print(f"\n=== 分析结果 ===")
    print(f"总记录数: {results['total_records']}")
    print(f"处理记录数: {results['processed_records']}")
    print(f"包含公司的记录数: {results['summary']['records_with_companies']}")
    print(f"覆盖率: {results['summary']['coverage_rate']:.2%}")
    print(f"发现的唯一公司数: {results['companies_found']['unique_count']}")
    
    print(f"\n=== 高频公司 (前10) ===")
    for company, freq in list(results['company_frequency'].items())[:10]:
        print(f"{company}: {freq} 次")
    
    # 保存结果
    output_file = f"{table_name}_companies_analysis.csv"
    analyzer.save_results_to_csv(results, output_file)


if __name__ == "__main__":
    main()
