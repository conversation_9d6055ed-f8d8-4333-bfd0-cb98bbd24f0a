# 新闻公司实体抽取工具

这是一个专门用于从新闻报道中抽取公司实体的工具包，支持多种抽取方法和中文文本处理。

## 功能特点

- **多种抽取方法**: 支持规则匹配、关键词匹配、NER模型等
- **中文优化**: 专门针对中文新闻文本优化
- **数据库集成**: 直接从MySQL数据库读取新闻数据
- **批量处理**: 支持大规模新闻数据的批量分析
- **结果导出**: 支持CSV格式结果导出
- **行业分类**: 自动将公司按行业分类

## 安装依赖

### 基础版本
```bash
pip install -r requirements.txt
```

### 高级版本（可选）
```bash
# 安装spaCy和中文模型
pip install spacy
python -m spacy download zh_core_web_sm

# 安装HanLP
pip install hanlp

# 安装transformers（如需使用BERT模型）
pip install transformers torch
```

## 使用方法

### 1. 基础使用

```python
from company_extractor import CompanyExtractor

# 创建抽取器
extractor = CompanyExtractor()

# 抽取公司实体
text = "腾讯公司今日发布财报，阿里巴巴集团股价上涨3%。"
result = extractor.extract_companies(text, method='hybrid')

print("发现的公司:", result['companies'])
print("置信度分数:", result['confidence_scores'])
```

### 2. 数据库集成使用

```python
from news_company_analyzer import NewsCompanyAnalyzer

# 创建分析器
analyzer = NewsCompanyAnalyzer()

# 查看可用表
tables = analyzer.get_available_tables()
print("可用表:", tables)

# 分析指定表中的公司实体
results = analyzer.analyze_news_companies(
    table_name="your_news_table",
    text_columns=["title", "content"],  # 指定包含文本的列
    limit=1000,  # 限制处理的记录数
    method='hybrid'
)

# 查看结果
print(f"处理了 {results['processed_records']} 条记录")
print(f"发现 {results['companies_found']['unique_count']} 个唯一公司")
print("高频公司:", list(results['company_frequency'].items())[:10])

# 保存结果到CSV
analyzer.save_results_to_csv(results, "company_analysis_results.csv")
```

### 3. 高级NLP模型使用

```python
from advanced_company_extractor import AdvancedCompanyExtractor

# 创建高级抽取器（需要安装额外依赖）
extractor = AdvancedCompanyExtractor(use_spacy=True, use_hanlp=True)

# 使用多种方法抽取
result = extractor.extract_companies_advanced(text)

print("发现的公司:", result['companies'])
print("使用的方法:", result['methods_used'])

# 查看详细信息
for entity in result['detailed_entities']:
    print(f"{entity['text']} - 置信度: {entity['confidence']:.2f}")

# 行业分类
industry_classification = extractor.classify_companies_by_industry(result['companies'])
for industry, companies in industry_classification.items():
    print(f"{industry}: {companies}")
```

## 抽取方法说明

### 1. 规则匹配 (rules)
- 基于正则表达式匹配公司名称模式
- 适合标准格式的公司名
- 速度快，准确率较高

### 2. 关键词匹配 (keywords)
- 基于公司相关关键词进行匹配
- 结合中文分词技术
- 覆盖面广，但可能有误报

### 3. 混合方法 (hybrid)
- 结合规则匹配和关键词匹配
- 计算置信度分数
- **推荐使用**，平衡准确率和召回率

### 4. NER模型 (高级版本)
- 使用spaCy或HanLP的命名实体识别模型
- 准确率高，但需要额外安装模型
- 适合对准确率要求较高的场景

## 支持的公司类型

- 中文公司名：腾讯公司、阿里巴巴集团、中国平安保险股份有限公司
- 英文公司名：Apple Inc.、Microsoft Corp.、Google LLC
- 金融机构：各类银行、保险、证券、基金公司
- 科技公司：互联网、软件、电子、通信类公司
- 其他行业：制造、房地产、医药、能源等

## 输出格式

### 基础结果
```python
{
    'companies': ['腾讯公司', '阿里巴巴集团'],
    'confidence_scores': {
        '腾讯公司': 0.95,
        '阿里巴巴集团': 0.90
    }
}
```

### 详细结果
```python
{
    'total_records': 1000,
    'processed_records': 1000,
    'companies_found': {
        'unique_companies': ['腾讯公司', '阿里巴巴集团', ...],
        'unique_count': 156
    },
    'company_frequency': {
        '腾讯公司': 45,
        '阿里巴巴集团': 38,
        ...
    },
    'summary': {
        'coverage_rate': 0.67,
        'top_companies': [...],
        'avg_companies_per_record': 2.3
    }
}
```

## 性能优化建议

1. **批量处理**: 使用`limit`参数控制单次处理的记录数
2. **方法选择**: 
   - 快速处理：使用`rules`方法
   - 平衡效果：使用`hybrid`方法
   - 高精度：使用高级NLP模型
3. **内存管理**: 处理大量数据时分批进行
4. **结果缓存**: 对相同文本的结果进行缓存

## 常见问题

### Q: 如何提高抽取准确率？
A: 
1. 使用`hybrid`方法结合多种抽取策略
2. 根据具体领域调整关键词和正则模式
3. 使用高级NLP模型（spaCy、HanLP）

### Q: 如何处理大量数据？
A: 
1. 使用`limit`参数分批处理
2. 考虑使用多进程并行处理
3. 将结果保存到数据库而不是内存

### Q: 如何自定义公司识别规则？
A: 
1. 修改`CompanyExtractor`类中的`company_patterns`
2. 添加特定行业的关键词到`company_keywords`
3. 重写`_is_valid_company_name`方法

## 文件说明

- `company_extractor.py`: 基础公司实体抽取器
- `news_company_analyzer.py`: 数据库集成的新闻分析器
- `advanced_company_extractor.py`: 高级NLP模型抽取器
- `dataset.py`: 数据库连接配置
- `requirements.txt`: 依赖包列表

## 许可证

MIT License
