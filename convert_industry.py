import json
import pymysql  # 或 mysql-connector-python

# 1. 读取 JSON 文件
with open('D:\\产业数据\\数据\\industry_list.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 讲数据转为csv格式
import csv
with open('industry_list.csv', 'w', newline='', encoding='utf-8') as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(['group_name', 'title', 'value', 'company_count', 'disabled'])
    for group in data:
        for item in group['industry_list']:
            writer.writerow([item['group_name'], item['title'], item['value']])
