#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API返回的数据格式
"""

import requests
import json

def test_articles_list():
    """测试文章列表API"""
    print("🔍 测试文章列表API")
    
    try:
        response = requests.get('http://localhost:5000/api/articles?page=1&page_size=3', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if data.get('success'):
                articles = data.get('data', {}).get('articles', [])
                print(f"\n📰 获取到 {len(articles)} 篇文章")
                
                for i, article in enumerate(articles):
                    print(f"\n文章 {i+1}:")
                    print(f"  ID: {article.get('id')}")
                    print(f"  标题: {article.get('title', '')[:50]}...")
                    print(f"  news_tag: '{article.get('news_tag', 'MISSING')}'")
                    print(f"  来源: {article.get('source', '')}")
                    
                    # 测试文章详情
                    if i == 0:  # 只测试第一篇文章的详情
                        print(f"\n🔍 测试文章 {article['id']} 的详情API")
                        detail_response = requests.get(f'http://localhost:5000/api/articles/{article["id"]}', timeout=10)
                        
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            if detail_data.get('success'):
                                article_detail = detail_data.get('data', {}).get('article', {})
                                print(f"  详情页news_tag: '{article_detail.get('news_tag', 'MISSING')}'")
                            else:
                                print(f"  ❌ 详情API错误: {detail_data.get('error')}")
                        else:
                            print(f"  ❌ 详情API HTTP错误: {detail_response.status_code}")
            else:
                print(f"❌ API返回错误: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_chanye_data():
    """测试产经数据API"""
    print("🔍 测试产经数据API")

    try:
        response = requests.get('http://localhost:5001/api/chanye', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_industry_trends():
    """测试产业趋势API"""
    print("🔍 测试产业趋势API")

    try:
        response = requests.get('http://***************:20001/chanye/api/industry-trends', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_regional_trends():
    """测试区域排行API"""
    print("🔍 测试区域排行API")

    try:
        response = requests.get('http://***************:20001/chanye/api/regional-trends', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_hot_companies():
    """测试热点企业API"""
    print("🔍 测试热点企业API")

    try:
        response = requests.get('http://***************:20001/chanye/api/hot-companies', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_policy_analysis():
    """测试政策解读API"""
    print("🔍 测试政策解读API")

    try:
        response = requests.get('http://***************:20001/chanye/api/policy-analysis', timeout=10)

        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_chanye_news():
    """测试完整产经数据API"""
    print("\n🔍 测试完整产经数据API")
    print("-" * 40)

    try:
        # 测试不同时间范围
        time_ranges = ['today', 'week', 'month']

        for time_range in time_ranges:
            print(f"\n📅 测试时间范围: {time_range}")
            response = requests.get(f'http://***************:20001/chanye/api/chanye_news?time_range={time_range}', timeout=15)

            if response.status_code == 200:
                data = response.json()
                print(json.dumps(data, indent=2, ensure_ascii=False))
                # if data.get('success'):
                #     print(f"✅ {time_range} 数据获取成功")
                #     chanye_data = data.get('data', {})

                #     # 检查各个模块的数据
                #     modules = ['industry_news', 'regional_news', 'company_news', 'policy_news']
                #     for module in modules:
                #         if module in chanye_data:
                #             module_data = chanye_data[module]
                #             data_count = len(module_data.get('data', []))
                #             print(f"  📊 {module_data.get('title', module)}: {data_count} 条数据")
                #         else:
                #             print(f"  ❌ 缺少模块: {module}")
                # else:
                #     print(f"❌ API返回错误: {data.get('error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_industry_news():
    """测试产业新闻API"""
    print("\n🔍 测试产业新闻API")
    print("-" * 40)

    try:
        response = requests.get('http://***************:20001/chanye/api/industry-news?time_range=month', timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(json.dumps(data, indent=2, ensure_ascii=False))
                print("✅ 产业新闻API响应成功")
                news_data = data.get('data', {})
                articles = news_data.get('data', [])
                print(f"📰 获取到 {len(articles)} 条产业新闻")

                # 显示前3条新闻的基本信息
                for i, article in enumerate(articles[:3]):
                    print(f"  {i+1}. {article.get('title', '无标题')[:50]}...")
                    print(f"     来源: {article.get('source', '未知')} | 标签: {article.get('news_tag', '未分类')}")
            else:
                print(f"❌ API返回错误: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_policy_news():
    """测试政策新闻API"""
    print("\n🔍 测试政策新闻API")
    print("-" * 40)

    try:
        response = requests.get('http://***************:20001/chanye/api/policy-news?time_range=month', timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(json.dumps(data, indent=2, ensure_ascii=False))
                print("✅ 政策新闻API响应成功")
                news_data = data.get('data', {})
                articles = news_data.get('data', [])
                print(f"📋 获取到 {len(articles)} 条政策新闻")

                # 显示前3条新闻的基本信息
                for i, article in enumerate(articles[:3]):
                    print(f"  {i+1}. {article.get('title', '无标题')[:50]}...")
                    print(f"     来源: {article.get('source', '未知')} | 时间: {article.get('publish_time', '未知')}")
            else:
                print(f"❌ API返回错误: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_company_news():
    """测试企业新闻API"""
    print("\n🔍 测试企业新闻API")
    print("-" * 40)

    try:
        response = requests.get('http://***************:20001/chanye/api/company-news?time_range=week', timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(json.dumps(data, indent=2, ensure_ascii=False))
                print("✅ 企业新闻API响应成功")
                news_data = data.get('data', {})
                articles = news_data.get('data', [])
                print(f"🏢 获取到 {len(articles)} 条企业新闻")

                # 显示前3条新闻的基本信息
                for i, article in enumerate(articles[:3]):
                    print(f"  {i+1}. {article.get('title', '无标题')[:50]}...")
                    print(f"     来源: {article.get('source', '未知')} | 标签: {article.get('news_tag', '未分类')}")
            else:
                print(f"❌ API返回错误: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_region_news():
    """测试区域新闻API"""
    print("\n🔍 测试区域新闻API")
    print("-" * 40)

    try:
        response = requests.get('http://***************:20001/chanye/api/region-news?time_range=month', timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 区域新闻API响应成功")
                news_data = data.get('data', {})
                articles = news_data.get('data', [])
                print(f"🌏 获取到 {len(articles)} 条区域新闻")

                # 显示前3条新闻的基本信息
                for i, article in enumerate(articles[:3]):
                    print(f"  {i+1}. {article.get('title', '无标题')[:50]}...")
                    print(f"     来源: {article.get('source', '未知')} | 时间: {article.get('publish_time', '未知')}")
            else:
                print(f"❌ API返回错误: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_api_with_different_time_ranges():
    """测试不同时间范围参数"""
    print("\n🔍 测试不同时间范围参数")
    print("-" * 40)

    apis = [
        ('产业趋势', '/api/industry-trends'),
        ('区域热点', '/api/regional-trends'),
        ('热点企业', '/api/hot-companies'),
        ('政策解读', '/api/policy-analysis')
    ]

    time_ranges = ['today', 'week', 'month', 'all']

    for api_name, api_path in apis:
        print(f"\n📊 测试 {api_name} API:")

        for time_range in time_ranges:
            try:
                response = requests.get(f'http://localhost:5001{api_path}?time_range={time_range}', timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        api_data = data.get('data', {})
                        data_count = len(api_data.get('data', []))
                        print(f"  ✅ {time_range}: {data_count} 条数据")
                    else:
                        print(f"  ❌ {time_range}: API错误 - {data.get('error')}")
                else:
                    print(f"  ❌ {time_range}: HTTP错误 {response.status_code}")

            except Exception as e:
                print(f"  ❌ {time_range}: 请求失败 - {str(e)}")

def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试错误处理")
    print("-" * 40)

    # 测试无效的时间范围参数
    print("📋 测试无效时间范围参数:")
    try:
        response = requests.get('http://localhost:5001/api/chanye_news?time_range=invalid', timeout=10)

        if response.status_code == 400:
            data = response.json()
            print(f"✅ 正确返回400错误: {data.get('error')}")
        else:
            print(f"❌ 未正确处理无效参数，状态码: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

    # 测试不存在的接口
    print("\n📋 测试不存在的接口:")
    try:
        response = requests.get('http://localhost:5001/api/nonexistent', timeout=10)
        print(f"✅ 不存在接口返回状态码: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def main():
    print("🚀 产经资讯系统 API 完整测试")
    print("=" * 60)

    # 基础功能测试
    # test_chanye_news()           # 完整产经数据
    test_industry_trends()       # 产业趋势
    # test_regional_trends()       # 区域热点
    # test_hot_companies()         # 热点企业
    # test_policy_analysis()       # 政策解读

    # # 新闻类API测试
    test_industry_news()         # 产业新闻
    # test_policy_news()           # 政策新闻
    # test_company_news()          # 企业新闻
    # test_region_news()           # 区域新闻

    # # 参数测试
    # test_api_with_different_time_ranges()  # 不同时间范围

    # # 错误处理测试
    # test_error_handling()        # 错误处理

    print("\n🎉 API测试完成!")

if __name__ == "__main__":
    main()
